package br.ufs.sicad.services;

import br.ufs.sicad.config.ResourceNotFoundException;
import br.ufs.sicad.domain.entidades.Aditivo;
import br.ufs.sicad.domain.entidades.Contrato;
import br.ufs.sicad.domain.entidades.Fornecedor;
import br.ufs.sicad.domain.entidades.Item;
import br.ufs.sicad.domain.enums.StatusContrato;
import br.ufs.sicad.infrastructure.repositories.AditivoRepository;
import br.ufs.sicad.infrastructure.repositories.ContratoRepository;
import br.ufs.sicad.infrastructure.repositories.FornecedorRepository;
import br.ufs.sicad.infrastructure.repositories.ItemRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Service
public class ContratoService {
    
    private final ContratoRepository contratoRepository;
    private final FornecedorRepository fornecedorRepository;
    private final ItemRepository itemRepository;
    private final AditivoRepository aditivoRepository;

    public ContratoService(ContratoRepository contratoRepository, 
                          FornecedorRepository fornecedorRepository,
                          ItemRepository itemRepository,
                          AditivoRepository aditivoRepository) {
        this.contratoRepository = contratoRepository;
        this.fornecedorRepository = fornecedorRepository;
        this.itemRepository = itemRepository;
        this.aditivoRepository = aditivoRepository;
    }

    @Transactional
    public Contrato criarContrato(Contrato contrato, List<Long> fornecedorIds, List<Long> itemIds) {
        if (fornecedorIds != null && !fornecedorIds.isEmpty()) {
            List<Fornecedor> fornecedores = new ArrayList<>();
            for (Long fornecedorId : fornecedorIds) {
                Fornecedor fornecedor = fornecedorRepository.findById(fornecedorId)
                        .orElseThrow(() -> new ResourceNotFoundException("Fornecedor não encontrado. ID: " + fornecedorId));
                fornecedores.add(fornecedor);
            }
            contrato.setFornecedores(fornecedores);
        }

        if (itemIds != null && !itemIds.isEmpty()) {
            List<Item> itens = itemRepository.findByIdIn(itemIds);
            if (itens.size() != itemIds.size()) {
                throw new ResourceNotFoundException("Alguns itens não foram encontrados");
            }
            contrato.setItens(itens);
        }

        return contratoRepository.save(contrato);
    }

    public Page<Contrato> listarContratos(String numContrato, String status, LocalDate dataInicial, 
                                         LocalDate dataFinal, List<Long> fornecedorIds, List<Integer> itemIds, Pageable pageable) {
        Page<Contrato> contratos = contratoRepository.findByFilters(numContrato, status, dataInicial, dataFinal, pageable);
        
        if (fornecedorIds != null && !fornecedorIds.isEmpty()) {
            List<Contrato> contratosFiltrados = contratos.getContent().stream()
                    .filter(contrato -> contrato.getFornecedores().stream()
                            .anyMatch(fornecedor -> fornecedorIds.contains(fornecedor.getId())))
                    .toList();
            
            Page<Contrato> contratosFiltradosPage = new org.springframework.data.domain.PageImpl<>(
                    contratosFiltrados, pageable, contratosFiltrados.size());
            contratos = contratosFiltradosPage;
        }
        
        if (itemIds != null && !itemIds.isEmpty()) {
            List<Contrato> contratosFiltrados = contratos.getContent().stream()
                    .filter(contrato -> contrato.getItens().stream()
                            .anyMatch(item -> itemIds.contains(item.getId())))
                    .toList();
            
            Page<Contrato> contratosFiltradosPage = new org.springframework.data.domain.PageImpl<>(
                    contratosFiltrados, pageable, contratosFiltrados.size());
            contratos = contratosFiltradosPage;
        }
        
        return contratos;
    }

    public Contrato buscarContratoPor(Integer id) {
        return contratoRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Contrato não encontrado. ID: " + id));
    }

    public List<Contrato> listarTodosContratos() {
        return contratoRepository.findAll();
    }

    @Transactional
    public Contrato atualizarContrato(Integer id, Contrato contratoAtualizado, List<Long> fornecedorIds, List<Long> itemIds) {
        Contrato contrato = buscarContratoPor(id);
        

        contrato.setNumContrato(contratoAtualizado.getNumContrato());
        contrato.setObjetoContrato(contratoAtualizado.getObjetoContrato());
        contrato.setValorTotal(contratoAtualizado.getValorTotal());
        contrato.setStatus(contratoAtualizado.getStatus() != null ? contratoAtualizado.getStatus() : StatusContrato.VIGENTE);
        contrato.setDataInicial(contratoAtualizado.getDataInicial());
        contrato.setDataFinal(contratoAtualizado.getDataFinal());
        
        if (fornecedorIds != null) {
            List<Fornecedor> fornecedores = new ArrayList<>();
            for (Long fornecedorId : fornecedorIds) {
                Fornecedor fornecedor = fornecedorRepository.findById(fornecedorId)
                        .orElseThrow(() -> new ResourceNotFoundException("Fornecedor não encontrado. ID: " + fornecedorId));
                fornecedores.add(fornecedor);
            }
            contrato.setFornecedores(fornecedores);
        }
        
        if (itemIds != null) {
            List<Item> itens = itemRepository.findByIdIn(itemIds);
            if (itens.size() != itemIds.size()) {
                throw new ResourceNotFoundException("Alguns itens não foram encontrados");
            }
            contrato.setItens(itens);
        }
        
        if (contratoAtualizado.getAditivos() != null) {
            List<Aditivo> aditivos = new ArrayList<>();
            for (Aditivo aditivo : contratoAtualizado.getAditivos()) {
                aditivo.setContrato(contrato);
                aditivos.add(aditivo);
            }
            contrato.setAditivos(aditivos);
        }
        
        return contratoRepository.save(contrato);
    }
} 
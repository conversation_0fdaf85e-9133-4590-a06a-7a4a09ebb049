package br.ufs.sicad.services;

import br.ufs.sicad.api.forms.NotaEmpenhoComItensForm;
import br.ufs.sicad.api.forms.NotaEmpenhoContratoForm;
import br.ufs.sicad.api.forms.NotaEmpenhoContratoItemForm;
import br.ufs.sicad.api.forms.NotaEmpenhoItemForm;
import br.ufs.sicad.api.forms.NotaEmpenhoUpdateComItensForm;
import br.ufs.sicad.domain.entidades.*;
import br.ufs.sicad.domain.enums.StatusNotaEmpenho;
import br.ufs.sicad.infrastructure.repositories.NotaEmpenhoItemRepository;
import br.ufs.sicad.infrastructure.repositories.NotaEmpenhoRepository;
import br.ufs.sicad.infrastructure.repositories.specifications.NotaEmpenhoSpecification;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class NotaEmpenhoService {

    private final NotaEmpenhoRepository repository;
    private final NotaEmpenhoItemRepository notaEmpenhoItemRepository;
    private final FornecedorService fornecedorService;
    private final ContratoService contratoService;
    private final ItemService itemService;

    @Value("${file.upload-dir}")
    private String uploadDir;

    public Page<NotaEmpenho> listarComFiltros(String numero, String status, Pageable pageable) {
        Specification<NotaEmpenho> spec = NotaEmpenhoSpecification.comFiltros(numero, status);
        return repository.findAll(spec, pageable);
    }

    public NotaEmpenho buscarPorId(Long id) {
        return repository.findById(id)
                .orElseThrow(() -> new RuntimeException("Nota de Empenho não encontrada com o ID: " + id));
    }

    @Transactional
    public NotaEmpenho criarComItens(NotaEmpenhoComItensForm form) {
        if (repository.existsByNumeroAndStatus(form.numero(), StatusNotaEmpenho.ATIVA)) {
            throw new RuntimeException("Já existe uma nota de empenho ativa com este número.");
        }
        Fornecedor fornecedor = fornecedorService.buscarFornecedorPor(form.fornecedorId());
        Contrato contrato = form.contratoId() != null ? contratoService.buscarContratoPor(form.contratoId().intValue()) : null;
        NotaEmpenho notaEmpenho = new NotaEmpenho(form.numero(), form.dataEmissao(), form.prazoMaximoEntrega(), form.diasNotificacaoPrevia(), fornecedor);
        notaEmpenho.setContrato(contrato);
        BigDecimal valorTotalCalculado = BigDecimal.ZERO;
        for (NotaEmpenhoItemForm itemForm : form.itens()) {
            Item itemDoCatalogo = itemService.buscarPorId(itemForm.itemId());
            NotaEmpenhoItem novoItemDaNota = new NotaEmpenhoItem(notaEmpenho, itemDoCatalogo, itemForm.quantidade(), itemForm.valorUnitario());
            novoItemDaNota.setObservacoes(itemForm.observacoes());
            notaEmpenho.getItens().add(novoItemDaNota);
            valorTotalCalculado = valorTotalCalculado.add(novoItemDaNota.getValorTotal());
        }
        notaEmpenho.setValorTotal(valorTotalCalculado);
        return repository.save(notaEmpenho);
    }

    @Transactional
    public NotaEmpenho atualizar(Long id, NotaEmpenhoUpdateComItensForm form) {
        NotaEmpenho notaEmpenho = repository.findByIdAndStatus(id, StatusNotaEmpenho.ATIVA)
                .orElseThrow(() -> new RuntimeException("Nota de Empenho ativa não encontrada para atualização"));
        if (!notaEmpenho.getNumero().equals(form.numero()) && repository.existsByNumeroAndStatusAndIdNot(form.numero(), StatusNotaEmpenho.ATIVA, id)) {
            throw new RuntimeException("Já existe outra nota de empenho ativa com este número");
        }
        notaEmpenho.setNumero(form.numero());
        notaEmpenho.setDataEmissao(form.dataEmissao());
        notaEmpenho.setPrazoMaximoEntrega(form.prazoMaximoEntrega());
        notaEmpenho.setDiasNotificacaoPrevia(form.diasNotificacaoPrevia());
        notaEmpenho.getItens().clear();
        BigDecimal valorTotalCalculado = BigDecimal.ZERO;
        for (NotaEmpenhoItemForm itemForm : form.itens()) {
            Item itemDoCatalogo = itemService.buscarPorId(itemForm.itemId());
            NotaEmpenhoItem novoItemDaNota = new NotaEmpenhoItem(notaEmpenho, itemDoCatalogo, itemForm.quantidade(), itemForm.valorUnitario());
            novoItemDaNota.setObservacoes(itemForm.observacoes());
            notaEmpenho.getItens().add(novoItemDaNota);
            valorTotalCalculado = valorTotalCalculado.add(novoItemDaNota.getValorTotal());
        }
        notaEmpenho.setValorTotal(valorTotalCalculado);
        return repository.save(notaEmpenho);
    }

    @Transactional
    public void deletar(Long id) {
        NotaEmpenho notaEmpenho = repository.findByIdAndStatus(id, StatusNotaEmpenho.ATIVA)
                .orElseThrow(() -> new RuntimeException("Nota de Empenho ativa não encontrada para desativar"));
        notaEmpenho.inativar();
        repository.save(notaEmpenho);
    }

    @Transactional
    public void reativar(Long id) {
        NotaEmpenho notaParaReativar = repository.findById(id)
                .orElseThrow(() -> new RuntimeException("Nota de Empenho não encontrada para reativar"));

        if (repository.existsByNumeroAndStatusAndIdNot(notaParaReativar.getNumero(), StatusNotaEmpenho.ATIVA, id)) {
            throw new RuntimeException(
                    "Não é possível reativar, pois já existe outra nota de empenho ativa com o número: " + notaParaReativar.getNumero()
            );
        }

        notaParaReativar.ativar();
        repository.save(notaParaReativar);
    }

    @Transactional
    public void adicionarAnexo(Long id, MultipartFile arquivo) throws IOException {
        if (arquivo == null || arquivo.isEmpty()) {
            throw new IllegalArgumentException("Arquivo inválido.");
        }

        // Validar se é um arquivo PDF
        String contentType = arquivo.getContentType();
        if (!"application/pdf".equals(contentType)) {
            throw new IllegalArgumentException("Apenas arquivos PDF são permitidos.");
        }

        NotaEmpenho notaEmpenho = buscarPorId(id);
        removerAnexoExistente(notaEmpenho);
        String nomeOriginal = StringUtils.cleanPath(arquivo.getOriginalFilename());
        String extensao = StringUtils.getFilenameExtension(nomeOriginal);

        // Validar extensão do arquivo
        if (!"pdf".equalsIgnoreCase(extensao)) {
            throw new IllegalArgumentException("Apenas arquivos com extensão .pdf são permitidos.");
        }
        String nomeArquivoUnico = UUID.randomUUID().toString() + (extensao != null ? "." + extensao : "");
        Path diretorioUpload = Paths.get(uploadDir);
        if (!Files.exists(diretorioUpload)) Files.createDirectories(diretorioUpload);
        Files.copy(arquivo.getInputStream(), diretorioUpload.resolve(nomeArquivoUnico));
        notaEmpenho.setUrlAnexo(nomeArquivoUnico);
        repository.save(notaEmpenho);
    }

    @Transactional
    public void removerAnexo(Long id) throws IOException {
        NotaEmpenho notaEmpenho = buscarPorId(id);
        removerAnexoExistente(notaEmpenho);
    }

    private void removerAnexoExistente(NotaEmpenho nota) throws IOException {
        if (nota.getUrlAnexo() != null && !nota.getUrlAnexo().isEmpty()) {
            Path arquivoPath = Paths.get(uploadDir).resolve(nota.getUrlAnexo());
            if (Files.exists(arquivoPath)) Files.delete(arquivoPath);
            nota.setUrlAnexo(null);
            repository.save(nota);
        }
    }

    @Transactional
    public void liquidarItem(Long itemEmpenhoId, Integer quantidadeParaLiquidar, BigDecimal valorParaLiquidar) {
        NotaEmpenhoItem item = notaEmpenhoItemRepository.findById(itemEmpenhoId)
                .orElseThrow(() -> new RuntimeException("Item de empenho não encontrado com ID: " + itemEmpenhoId));

        int novaQuantidadeLiquidada = item.getQuantidadeLiquidada() + quantidadeParaLiquidar;
        if (novaQuantidadeLiquidada > item.getQuantidade()) {
            throw new IllegalArgumentException("A quantidade a liquidar (" + quantidadeParaLiquidar +
                    ") excede a quantidade restante empenhada (" + (item.getQuantidade() - item.getQuantidadeLiquidada()) + ").");
        }

        BigDecimal novoValorLiquidado = item.getValorLiquidado().add(valorParaLiquidar);
        if (novoValorLiquidado.compareTo(item.getValorTotal()) > 0) {
            throw new IllegalArgumentException("O valor a liquidar excede o valor total empenhado para este item.");
        }

        item.setQuantidadeLiquidada(novaQuantidadeLiquidada);
        item.setValorLiquidado(novoValorLiquidado);

        notaEmpenhoItemRepository.save(item);
    }

    @Transactional
    public NotaEmpenho criarNotaEmpenhoDeContrato(NotaEmpenhoContratoForm form) {
        // Validar se o contrato existe
        Contrato contrato = contratoService.buscarContratoPor(form.contratoId());

        // Validar se o fornecedor existe
        Fornecedor fornecedor = fornecedorService.buscarPorId(form.fornecedorId());

        // Validar se o fornecedor está associado ao contrato
        boolean fornecedorNoContrato = contrato.getFornecedores().stream()
                .anyMatch(f -> f.getId().equals(form.fornecedorId()));

        if (!fornecedorNoContrato) {
            throw new IllegalArgumentException("O fornecedor não está associado ao contrato informado");
        }

        // Criar a nota de empenho
        NotaEmpenho notaEmpenho = new NotaEmpenho();
        notaEmpenho.setNumero(form.numero());
        notaEmpenho.setDataEmissao(form.dataEmissao());
        notaEmpenho.setPrazoMaximoEntrega(form.prazoMaximoEntrega());
        notaEmpenho.setDiasNotificacaoPrevia(form.diasNotificacaoPrevia());
        notaEmpenho.setFornecedor(fornecedor);
        notaEmpenho.setContrato(contrato);

        // Salvar a nota de empenho primeiro para obter o ID
        notaEmpenho = repository.save(notaEmpenho);

        // Processar os itens e calcular valor total
        BigDecimal valorTotal = BigDecimal.ZERO;
        List<NotaEmpenhoItem> itensNotaEmpenho = new ArrayList<>();

        for (NotaEmpenhoContratoItemForm itemForm : form.itens()) {
            // Validar se o item existe no contrato
            Item item = itemService.buscarPorId(itemForm.itemId());
            boolean itemNoContrato = contrato.getItens().stream()
                    .anyMatch(i -> i.getId().equals(itemForm.itemId()));

            if (!itemNoContrato) {
                throw new IllegalArgumentException("O item ID " + itemForm.itemId() + " não está associado ao contrato");
            }

            // Criar item da nota de empenho
            NotaEmpenhoItem notaEmpenhoItem = new NotaEmpenhoItem();
            notaEmpenhoItem.setNotaEmpenho(notaEmpenho);
            notaEmpenhoItem.setItem(item);
            notaEmpenhoItem.setQuantidade(itemForm.quantidade());
            notaEmpenhoItem.setValorUnitario(itemForm.valorUnitario());
            notaEmpenhoItem.setObservacoes(itemForm.observacoes());

            itensNotaEmpenho.add(notaEmpenhoItem);

            // Calcular valor total
            BigDecimal valorItem = itemForm.valorUnitario().multiply(BigDecimal.valueOf(itemForm.quantidade()));
            valorTotal = valorTotal.add(valorItem);
        }

        // Salvar os itens
        notaEmpenhoItemRepository.saveAll(itensNotaEmpenho);

        // Atualizar o valor total da nota de empenho
        notaEmpenho.setValorTotal(valorTotal);
        notaEmpenho.setItens(itensNotaEmpenho);

        return repository.save(notaEmpenho);
    }
}
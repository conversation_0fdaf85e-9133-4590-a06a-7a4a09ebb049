//package br.ufs.sicad.services;
//
//import br.ufs.sicad.config.ResourceNotFoundException;
//import br.ufs.sicad.domain.entidades.Documento;
//import br.ufs.sicad.domain.entidades.UnidadeOrganizacional;
//import br.ufs.sicad.domain.entidades.Usuario;
//import br.ufs.sicad.infrastructure.repositories.DocumentoRepository;
//import jakarta.annotation.PostConstruct;
//import jakarta.transaction.Transactional;
//import lombok.RequiredArgsConstructor;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.core.io.Resource;
//import org.springframework.core.io.UrlResource;
//import org.springframework.stereotype.Service;
//import org.springframework.util.StringUtils;
//import org.springframework.web.multipart.MultipartFile;
//
//import java.io.IOException;
//import java.io.InputStream;
//import java.net.MalformedURLException;
//import java.nio.file.Files;
//import java.nio.file.Path;
//import java.nio.file.Paths;
//import java.nio.file.StandardCopyOption;
//import java.time.LocalDateTime;
//import java.util.UUID;
//
//@Service
//@RequiredArgsConstructor
//public class DocumentoService {
//
//    private final DocumentoRepository documentoRepository;
//
//    @Value("${file.upload-dir}")
//    private String uploadDir;
//
//    private Path rootLocation;
//
//    @PostConstruct
//    public void init() throws IOException {
//        rootLocation = Paths.get(uploadDir);
//        Files.createDirectories(rootLocation);
//    }
//
//    @Transactional
//    public Documento salvarArquivo(MultipartFile file, UnidadeOrganizacional unidade, Usuario usuario) {
//        String nomeOriginal = StringUtils.cleanPath(file.getOriginalFilename());
//
//        String extensao = StringUtils.getFilenameExtension(nomeOriginal);
//        String nomeBase = String.format("%04d%s%s",
//                unidade.getId(),
//                unidade.getSigla().replaceAll("\\s", ""),
//                usuario.getNome().replaceAll("\\s", "")
//        );
//        String nomeUnico = nomeBase + "_" + UUID.randomUUID().toString() + "." + extensao;
//
//        Path destinationFile = this.rootLocation.resolve(Paths.get(nomeUnico)).normalize().toAbsolutePath();
//        try (InputStream inputStream = file.getInputStream()) {
//            Files.copy(inputStream, destinationFile, StandardCopyOption.REPLACE_EXISTING);
//        } catch (IOException e) {
//            throw new RuntimeException("Falha ao salvar o arquivo.", e);
//        }
//
//        Documento doc = new Documento();
//        doc.setNomeArquivoOriginal(nomeOriginal);
//        doc.setNomeArquivoUnico(nomeUnico);
//        doc.setTipoArquivo(file.getContentType());
//        doc.setCaminhoArquivo(destinationFile.toString());
//        doc.setDataUpload(LocalDateTime.now());
//        // O status já é 'ATIVO' por padrão na entidade
//
//        return documentoRepository.save(doc);
//    }
//
//    public Resource carregarComoRecurso(String nomeArquivoUnico) {
//        try {
//            Documento doc = documentoRepository.findByNomeArquivoUnico(nomeArquivoUnico)
//                    .orElseThrow(() -> new ResourceNotFoundException("Documento não encontrado."));
//
//            Path file = Paths.get(doc.getCaminhoArquivo());
//            Resource resource = new UrlResource(file.toUri());
//
//            if (resource.exists() && resource.isReadable()) {
//                return resource;
//            } else {
//                throw new RuntimeException("Não foi possível ler o arquivo: " + nomeArquivoUnico);
//            }
//        } catch (MalformedURLException e) {
//            throw new RuntimeException("Erro no caminho do arquivo: " + nomeArquivoUnico, e);
//        }
//    }
//
//    public Documento buscarPorId(Long id) {
//        return documentoRepository.findById(id)
//                .orElseThrow(() -> new ResourceNotFoundException("Documento com id " + id + " não encontrado."));
//    }
//
//    @Transactional
//    public void inativar(Long id) {
//        Documento doc = buscarPorId(id);
//        doc.inativar(); // Chama o método na própria entidade
//        documentoRepository.save(doc);
//    }
//}
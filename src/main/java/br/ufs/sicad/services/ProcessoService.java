package br.ufs.sicad.services;

import br.ufs.sicad.api.dtos.RegistroDistribuicaoForm;
import br.ufs.sicad.config.ResourceNotFoundException;
import br.ufs.sicad.config.ValidationException;
import br.ufs.sicad.domain.entidades.*;
import br.ufs.sicad.domain.enums.Modalidade;
import br.ufs.sicad.domain.enums.StatusProcesso;
import br.ufs.sicad.infrastructure.repositories.ItemRepository;
import br.ufs.sicad.infrastructure.repositories.ProcessoRepository;
import br.ufs.sicad.infrastructure.repositories.UnidadeOrganizacionalRepository;
import br.ufs.sicad.infrastructure.repositories.UsuarioRepository;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.Set;

@Service
public class ProcessoService {

    private final ProcessoRepository processoRepository;
    private final UsuarioRepository usuarioRepository;
    private final UnidadeOrganizacionalRepository unidadeRepository;
    private final ItemRepository itemRepository;

    public ProcessoService(ProcessoRepository processoRepository, UsuarioRepository usuarioRepository, UnidadeOrganizacionalRepository unidadeRepository, ItemRepository itemRepository) {
        this.processoRepository = processoRepository;
        this.usuarioRepository = usuarioRepository;
        this.unidadeRepository = unidadeRepository;
        this.itemRepository = itemRepository;
    }

    public Page<Processo> listarProcessos(String numeroSei, String tipo, Modalidade modalidade, StatusProcesso status, Pageable pageable) {
        Class<? extends Processo> tipoClasse = null;

        if (tipo != null && !tipo.isBlank()) {
            tipoClasse = switch (tipo.toUpperCase()) {
                case "SPAL" -> ProcessoSpal.class;
                case "SERVICO" -> ProcessoServico.class;
                case "CONSUMO" -> ProcessoConsumo.class;
                case "PERMANENTE" -> ProcessoPermanente.class;
                // Adicione o ProcessoConsumo se ele existir
                // case "CONSUMO" -> ProcessoConsumo.class;
                default -> throw new ValidationException("Tipo de processo desconhecido: " + tipo);
            };
        }
        return processoRepository.findByFilters(numeroSei, tipoClasse, modalidade, status, pageable);
    }

    public Processo buscarProcessoPorId(Long id) {
        return processoRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Processo com ID " + id + " não encontrado."));
    }

    @Transactional
    public Processo criarProcesso(Processo novoProcesso, Set<Long> processosRelacionadosIds) {
        Usuario criador = usuarioRepository.findById(novoProcesso.getCriador().getId())
                .orElseThrow(() -> new ResourceNotFoundException("Usuário criador não encontrado."));

        UnidadeOrganizacional unidade = unidadeRepository.findById(novoProcesso.getUnidadeRequisitante().getId())
                .orElseThrow(() -> new ResourceNotFoundException("Unidade requisitante não encontrada."));

        if (processosRelacionadosIds != null) {
            Set<Processo> processosRelacionados = new HashSet<>();
            for (Long processoId : processosRelacionadosIds) {
                processosRelacionados.add(buscarProcessoPorId(processoId));
            }
            novoProcesso.setProcessosRelacionados(processosRelacionados);
        }

        novoProcesso.setCriador(criador);
        novoProcesso.setUnidadeRequisitante(unidade);

        return processoRepository.save(novoProcesso);
    }

    @Transactional
    public Processo atualizarProcesso(Long id, Processo processoAtualizado) {
        Processo processoAntigo = buscarProcessoPorId(id);
        if (!processoAtualizado.getClass().getSimpleName().equalsIgnoreCase(processoAntigo.getClass().getSimpleName())) {
            throw new ValidationException("Tipo de processo incompatível com id.");
        }
        processoAtualizado.setId(id);
        processoAtualizado.setModalidade(processoAntigo.getModalidade());
        return processoRepository.save(processoAtualizado);
    }

    
    @Transactional
    public Processo arquivarProcesso(Long id) {
        Processo processo = buscarProcessoPorId(id);
        // Aqui você pode adicionar validações, se necessário
        processo.setStatus(StatusProcesso.ARQUIVADO);
        return processoRepository.save(processo);
    }

    @Transactional
    public Processo ativarProcesso(Long id) {
        Processo processo = buscarProcessoPorId(id);
        processo.setStatus(StatusProcesso.ATIVO);
        return processoRepository.save(processo);
    }

    @Transactional
    public Processo adicionarSolicitacaoSpal(Long id, Long requerenteInternoId, SolicitacaoSpal solicitacao) {
        if (requerenteInternoId != null) {
            Usuario requerente = usuarioRepository.findById(requerenteInternoId)
                    .orElseThrow(() -> new ResourceNotFoundException("Usuário requerente não encontrado."));
            solicitacao.setRequerenteInterno(requerente);
        }

        Processo processo = buscarProcessoPorId(id);
        if (!(processo instanceof ProcessoSpal)) {
            throw new ValidationException("Este processo não é um processo Spal.");
        }
        ProcessoSpal processoSpal = (ProcessoSpal) processo;

        processoSpal.adicionarSolicitacao(solicitacao);
        return processoRepository.save(processoSpal);
    }    

    @Transactional
    public Processo adicionarRegistroDistribuicao(Long processoId, RegistroDistribuicaoForm form) {
        Processo processo = buscarProcessoPorId(processoId);
        ProcessoServico processoServico;
        ProcessoPermanente processoPermanente;

        UnidadeOrganizacional unidadeDestino = unidadeRepository.findById(form.unidadeDestinoId())
                .orElseThrow(() -> new ResourceNotFoundException("Unidade de destino com ID " + form.unidadeDestinoId() + " não encontrada."));

        Item item = itemRepository.findById(form.itemId())
                .orElseThrow(() -> new ResourceNotFoundException("Item com ID " + form.itemId() + " não encontrado."));

        RegistroDistribuicao novoRegistro = new RegistroDistribuicao();
        novoRegistro.setDataDistribuicao(form.dataDistribuicao());
        novoRegistro.setQuantidadeDistribuida(form.quantidadeDistribuida());
        novoRegistro.setUnidadeDestino(unidadeDestino);
        novoRegistro.setItem(item);
        novoRegistro.setPatrimonios(form.patrimonios());

        if (processo instanceof ProcessoServico){
            processoServico = (ProcessoServico) processo;
            processoServico.adicionarRegistroDistribuicao(novoRegistro);
        } else if (processo instanceof ProcessoPermanente) {
            processoPermanente = (ProcessoPermanente) processo;
            processoPermanente.adicionarRegistroDistribuicao(novoRegistro);
        } else {
            throw new ValidationException("Este tipo de processo não suporta registos de distribuição.");
        }

        return processoRepository.save(processo);
    }
}
//package br.ufs.sicad.services;
//
//import br.ufs.sicad.domain.entidades.Documento;
//import br.ufs.sicad.domain.entidades.SolicitacaoSpal;
//import lombok.RequiredArgsConstructor;
//import org.apache.pdfbox.pdmodel.PDDocument;
//import org.apache.pdfbox.pdmodel.PDPage;
//import org.apache.pdfbox.pdmodel.PDPageContentStream;
//import org.apache.pdfbox.pdmodel.font.PDType1Font;
////import org.springframework.mock.web.MockMultipartFile;
//import org.springframework.stereotype.Service;
//import org.springframework.web.multipart.MultipartFile;
//
//import java.io.ByteArrayOutputStream;
//import java.io.IOException;
//import java.time.LocalDate;
//import java.time.format.DateTimeFormatter;
//import java.util.Locale;
//
//@Service
//@RequiredArgsConstructor
//public class PdfGenerationService {
//
//    private final DocumentoService documentoService;
//
//    public Documento gerarAutorizacaoSpal(SolicitacaoSpal solicitacao) throws IOException {
//        //  Cria um documento PDF em memória
//        try (PDDocument document = new PDDocument()) {
//            PDPage page = new PDPage();
//            document.addPage(page);
//
//            //  Preenche o conteúdo do PDF
//            try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
//                escreverConteudo(contentStream, solicitacao);
//            }
//
//            // Salva o PDF em um array de bytes
//            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
//            document.save(outputStream);
//
//            // Cria um nome de arquivo e um MultipartFile a partir dos bytes do PDF
////            String fileName = "Autorizacao_SPAL_" + solicitacao.getId() + ".pdf";
////            MultipartFile multipartFile = new MockMultipartFile(
////                    fileName,
////                    fileName,
////                    "application/pdf",
////                    outputStream.toByteArray()
////            );
//
////            // Usa o DocumentoService para salvar o arquivo
////            return documentoService.salvarArquivo(
////                    multipartFile,
////                    solicitacao.getProcessoSpal().getUnidadeRequisitante(),
////                    solicitacao.getProcessoSpal().getCriador()
////            );
//            return null;
//        }
//    }
//
//    private void escreverConteudo(PDPageContentStream contentStream, SolicitacaoSpal solicitacao) throws IOException {
//        // --- CABEÇALHO ---
//        contentStream.beginText();
//        contentStream.setFont(PDType1Font.HELVETICA_BOLD, 12);
//        contentStream.newLineAtOffset(150, 750);
//        contentStream.showText("UNIVERSIDADE FEDERAL DE SERGIPE");
//        contentStream.newLine();
//        contentStream.showText("SUPERINTENDENCIA DE TECNOLOGIA DA INFORMAÇÃO");
//        contentStream.newLine();
//        contentStream.showText("COORDENAÇÃO DE SUPORTE");
//        contentStream.endText();
//
//        // --- TÍTULO ---
//        contentStream.beginText();
//        contentStream.setFont(PDType1Font.HELVETICA_BOLD, 14);
//        contentStream.newLineAtOffset(180, 680);
//        contentStream.showText("AUTORIZAÇÃO DE ADMINISTRADOR LOCAL");
//        contentStream.endText();
//
//        // --- DATA ---
//        LocalDate hoje = LocalDate.now();
//        Locale locale = new Locale("pt", "BR");
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd 'de' MMMM 'de' yyyy", locale);
//        String dataFormatada = hoje.format(formatter);
//        contentStream.beginText();
//        contentStream.setFont(PDType1Font.HELVETICA, 12);
//        contentStream.newLineAtOffset(100, 640);
//        contentStream.showText("Cidade Universitária \"Prof. José Aloisio de Campos\", " + dataFormatada);
//        contentStream.endText();
//
//
//        // --- CORPO DO TEXTO ---
//        String corpo = String.format(
//            "Autorizamos a permissão de administrador local conforme solicitado em Ofício nº %s " +
//            "sistema SEI ficando de responsabilidade do autorizado: %s Matrícula/CPF %s " +
//            "lotado na %s, a cumprir todas as diretrizes estabelecidas pelas normativas aprovadas por " +
//            "Portaria GSI/PR nº 93, de 26 de setembro de 2019 que regem está instituição aos ativos " +
//            "de TIC (Tecnologia da Informação e Comunicação), respondendo por quaisquer ônus (físico ou logico) " +
//            "gerados pelo descumprimento dessa normativa nos equipamentos sob nome e patrimônio: ",
//            solicitacao.getProcessoSpal().getNumeroSei(),
//            solicitacao.getNomeRequerente(),
//            solicitacao.getDocumentoRequerente(),null
////            solicitacao.isRequerenteInterno() ? solicitacao.getRequerenteUsuario().getUnidade().getSigla() : solicitacao.getRequerenteExternoUnidade()
//        );
//
//        // Lógica para quebrar o texto em várias linhas (simples)
//        contentStream.beginText();
//        contentStream.setFont(PDType1Font.HELVETICA, 12);
//        contentStream.setLeading(14.5f); // Espaçamento entre linhas
//        contentStream.newLineAtOffset(50, 600);
//
//        // Simples quebra de linha (pode ser aprimorado com um algoritmo mais complexo)
//        String[] linhas = corpo.split("(?<=\\G.{100})");
//        for (String linha : linhas) {
//            contentStream.showText(linha);
//            contentStream.newLine();
//        }
//        contentStream.endText();
//
//        // --- PATRIMÔNIOS ---
//        contentStream.beginText();
//        contentStream.setFont(PDType1Font.HELVETICA_BOLD, 12);
//        contentStream.newLineAtOffset(250, 500);
//        for(String patrimonio : solicitacao.getPatrimonios()){
//             contentStream.showText(patrimonio);
//             contentStream.newLine();
//        }
//        contentStream.endText();
//
//        // --- ASSINATURAS ---
//        contentStream.beginText();
//        contentStream.setFont(PDType1Font.HELVETICA, 12);
//        contentStream.newLineAtOffset(220, 200);
//        contentStream.showText("_________________________");
//        contentStream.newLineAtOffset(25, -15);
//        contentStream.showText("Chefe Departamento");
//        contentStream.endText();
//
//        contentStream.beginText();
//        contentStream.setFont(PDType1Font.HELVETICA, 12);
//        contentStream.newLineAtOffset(220, 150);
//        contentStream.showText("_________________________");
//        contentStream.newLineAtOffset(20, -15);
//        contentStream.showText("Servidor/Terceirizado");
//        contentStream.endText();
//
//        contentStream.beginText();
//        contentStream.setFont(PDType1Font.HELVETICA, 12);
//        contentStream.newLineAtOffset(220, 100);
//        contentStream.showText("_________________________");
//        contentStream.newLineAtOffset(5, -15);
//        contentStream.showText("Coordenador de Suporte (COSUP)");
//        contentStream.endText();
//    }
//}
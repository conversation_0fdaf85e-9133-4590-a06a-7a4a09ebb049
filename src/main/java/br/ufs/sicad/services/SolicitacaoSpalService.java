package br.ufs.sicad.services;

import br.ufs.sicad.config.ResourceNotFoundException;
import br.ufs.sicad.config.ValidationException;
//import br.ufs.sicad.domain.entidades.Documento;
import br.ufs.sicad.domain.entidades.Processo;
import br.ufs.sicad.domain.entidades.ProcessoSpal;
import br.ufs.sicad.domain.entidades.SolicitacaoSpal;
import br.ufs.sicad.domain.entidades.Usuario;
import br.ufs.sicad.domain.enums.StatusSolicitacaoSpal;
import br.ufs.sicad.infrastructure.repositories.ProcessoRepository;
import br.ufs.sicad.infrastructure.repositories.SolicitacaoSpalRepository;
import br.ufs.sicad.infrastructure.repositories.UsuarioRepository;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class SolicitacaoSpalService {

    private final ProcessoRepository processoRepository;
    private final UsuarioRepository usuarioRepository;
    private final SolicitacaoSpalRepository solicitacaoRepository;
//    private final PdfGenerationService pdfService;

    @Transactional
    public Processo adicionarSolicitacaoSpal(ProcessoSpal processo, Long requerenteInternoId, SolicitacaoSpal solicitacao){
        if (requerenteInternoId != null){
            Usuario requerente = usuarioRepository.findById(requerenteInternoId)
                    .orElseThrow(() -> new ResourceNotFoundException("Usuário requerente não encontrado."));
            solicitacao.setRequerenteInterno(requerente);
        }
        processo.adicionarSolicitacao(solicitacao);
        return processoRepository.save(processo);
    }
    
    public SolicitacaoSpal buscarPorId(Long id) {
        return solicitacaoRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Solicitação SPAL não encontrada."));
    }

    @Transactional
    public void arquivarSolicitacao(Long id) {
        SolicitacaoSpal solicitacao = buscarPorId(id);
        solicitacao.arquivar();
        solicitacaoRepository.save(solicitacao);
    }
    
    @Transactional
    public SolicitacaoSpal registrarParecerTecnico(Long solicitacaoId, String parecer) {
        SolicitacaoSpal solicitacao = buscarPorId(solicitacaoId);
        if (solicitacao.getStatusFluxo() != StatusSolicitacaoSpal.PENDENTE_ANALISE) {
            throw new ValidationException("Esta solicitação não está mais pendente de análise técnica.");
        }
        solicitacao.setAnaliseTecnica(parecer);
        solicitacao.setStatusFluxo(StatusSolicitacaoSpal.PENDENTE_APROVACAO);
        return solicitacaoRepository.save(solicitacao);
    }

    @Transactional
    public SolicitacaoSpal aprovar(Long solicitacaoId) {
        SolicitacaoSpal solicitacao = buscarPorId(solicitacaoId);
        if (solicitacao.getStatusFluxo() != StatusSolicitacaoSpal.PENDENTE_APROVACAO) {
            throw new ValidationException("Esta solicitação não pode ser aprovada no status atual.");
        }
        solicitacao.setStatusFluxo(StatusSolicitacaoSpal.APROVADA);
        solicitacao.setMotivoRejeicao(null);
        
//        try {
//            Documento pdf = pdfService.gerarAutorizacaoSpal(solicitacao);
//            solicitacao.setDocumentoDePermissao(pdf);
//        } catch (IOException e) {
//            throw new RuntimeException("Falha ao gerar o documento PDF da autorização.", e);
//        }

        return solicitacaoRepository.save(solicitacao);
    }

    @Transactional
    public SolicitacaoSpal recusar(Long solicitacaoId, String justificativa) {
        if (justificativa == null || justificativa.isBlank()) {
            throw new ValidationException("A justificativa da rejeição é obrigatório.");
        }
        SolicitacaoSpal solicitacao = buscarPorId(solicitacaoId);
        solicitacao.setStatusFluxo(StatusSolicitacaoSpal.REJEITADA);
        solicitacao.setMotivoRejeicao(justificativa);
        return solicitacaoRepository.save(solicitacao);
    }
}
package br.ufs.sicad.infrastructure.repositories;

import br.ufs.sicad.domain.entidades.NotaEmpenho;
import br.ufs.sicad.domain.enums.StatusNotaEmpenho;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface NotaEmpenhoRepository extends JpaRepository<NotaEmpenho, Long>, JpaSpecificationExecutor<NotaEmpenho> {

    // Busca uma nota por ID, independentemente do status.
    @Override
    Optional<NotaEmpenho> findById(Long id);

    // Busca uma nota por ID, mas somente se estiver com o status ATIVA.
    Optional<NotaEmpenho> findByIdAndStatus(Long id, StatusNotaEmpenho status);

    // Verifica se já existe uma nota com um determinado número e status.
    boolean existsByNumeroAndStatus(String numero, StatusNotaEmpenho status);

    // ***** MÉTODO ESSENCIAL PARA A REATIVAÇÃO *****
    // Verifica se já existe uma nota com um determinado número e status,
    // EXCLUINDO o ID da própria nota que estamos verificando.
    boolean existsByNumeroAndStatusAndIdNot(String numero, StatusNotaEmpenho status, Long id);

}
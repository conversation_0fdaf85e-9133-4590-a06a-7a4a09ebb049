package br.ufs.sicad.infrastructure.repositories;

import br.ufs.sicad.domain.entidades.Processo;
import br.ufs.sicad.domain.enums.Modalidade;
import br.ufs.sicad.domain.enums.StatusProcesso;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface ProcessoRepository extends JpaRepository<Processo, Long>, JpaSpecificationExecutor<Processo> {
    @Query("SELECT DISTINCT p FROM Processo p " +
            "WHERE " +
            "(:numeroSei IS NULL OR p.numeroSei LIKE %:numeroSei%) AND " +
            "(:tipo_processo IS NULL OR TYPE(p) = %:tipo_processo%) AND " +
            "(:modalidade IS NULL OR p.modalidade = %:modalidade%) AND " +
            "(:status IS NULL OR p.status = %:status%)")
    Page<Processo> findByFilters(
            @Param("numeroSei") String numeroSei,
            @Param("tipo_processo") Class<? extends Processo> tipo,
            @Param("modalidade") Modalidade modalidade,
            @Param("status") StatusProcesso status,
            Pageable pageable);
}
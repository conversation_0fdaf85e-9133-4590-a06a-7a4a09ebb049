//package br.ufs.sicad.infrastructure.repositories;
//
//import br.ufs.sicad.domain.entidades.Documento;
//import org.springframework.data.jpa.repository.JpaRepository;
//import org.springframework.stereotype.Repository;
//import java.util.Optional;
//
//@Repository
//public interface DocumentoRepository extends JpaRepository<Documento, Long> {
//    Optional<Documento> findByNomeArquivoUnico(String nomeArquivoUnico);
//}
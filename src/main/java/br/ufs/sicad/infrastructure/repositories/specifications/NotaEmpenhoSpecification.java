package br.ufs.sicad.infrastructure.repositories.specifications;

import br.ufs.sicad.domain.entidades.NotaEmpenho;
import br.ufs.sicad.domain.enums.StatusNotaEmpenho;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;

/**
 * Esta classe centraliza a lógica para a construção de queries dinâmicas
 * para a entidade NotaEmpenho usando a API de Criteria do JPA.
 * Permite a criação de filtros complexos de forma segura e reutilizável.
 */
public class NotaEmpenhoSpecification {

    /**
     * Cria uma {@link Specification} combinando os filtros de número e status.
     * Esta é a principal função usada pelo serviço para a listagem na tela de gerenciamento.
     *
     * @param numero O número da nota de empenho a ser buscado (parcial, case-insensitive).
     * @param status O status da nota ("ATIVA", "INATIVA" ou "TODAS").
     * @return Uma {@link Specification} que pode ser executada pelo repositório.
     */
    public static Specification<NotaEmpenho> comFiltros(String numero, String status) {
        // Combina as especificações. A cláusula WHERE é implícita.
        // Se uma specification for nula, ela é ignorada.
        return Specification.where(numeroContem(numero)).and(statusIgualA(status));
    }

    /**
     * Retorna uma Specification que filtra por parte do número da nota, ignorando maiúsculas/minúsculas.
     * Se o número for nulo ou vazio, retorna null, o que faz com que este filtro não seja aplicado.
     */
    private static Specification<NotaEmpenho> numeroContem(String numero) {
        if (!StringUtils.hasText(numero)) {
            return null;
        }
        return (root, query, criteriaBuilder) ->
                criteriaBuilder.like(criteriaBuilder.lower(root.get("numero")), "%" + numero.toLowerCase() + "%");
    }

    /**
     * Retorna uma Specification que filtra pelo status da nota.
     * Ignora o filtro se o status for nulo, "TODAS", ou um valor inválido.
     */
    private static Specification<NotaEmpenho> statusIgualA(String status) {
        if (!StringUtils.hasText(status) || status.equalsIgnoreCase("TODAS")) {
            return null;
        }
        try {
            StatusNotaEmpenho statusEnum = StatusNotaEmpenho.valueOf(status.toUpperCase());
            return (root, query, criteriaBuilder) ->
                    criteriaBuilder.equal(root.get("status"), statusEnum);
        } catch (IllegalArgumentException e) {
            // Se um status inválido for passado pela URL, simplesmente não filtra.
            return null;
        }
    }
}
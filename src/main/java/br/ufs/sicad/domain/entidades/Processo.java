package br.ufs.sicad.domain.entidades;

import br.ufs.sicad.domain.enums.Modalidade;
import br.ufs.sicad.domain.enums.StatusProcesso;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.util.Set;
import java.util.ArrayList;
import java.util.HashSet;
import java.time.LocalDate;
import java.util.List;


@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "processos")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "tipo_processo", discriminatorType = DiscriminatorType.STRING)
public abstract class Processo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "numero_sei", nullable = false, unique = true, length = 100)
    private String numeroSei;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private Modalidade modalidade;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private StatusProcesso status = StatusProcesso.ATIVO;

    @Column(nullable = false)
    private String nomeSolicitanteProcesso;

    @Column(name = "data_abertura", nullable = false)
    private LocalDate dataAbertura;

    @Column(columnDefinition = "TEXT")
    private String justificativa;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "criador_id", nullable = false)
    private Usuario criador;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "unidade_requisitante_id", nullable = false)
    private UnidadeOrganizacional unidadeRequisitante;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "processo_relacionamentos",
        joinColumns = @JoinColumn(name = "processo_origem_id"),
        inverseJoinColumns = @JoinColumn(name = "processo_relacionado_id")
    )
    private Set<Processo> processosRelacionados = new HashSet<>();

    public void adicionarProcessoRelacionado(Processo processo) {
        this.processosRelacionados.add(processo);
        processo.getProcessosRelacionados().add(this); // Manter a relação bidirecional
    }
    public void removerProcessoRelacionado(Processo processo) {
        this.processosRelacionados.remove(processo);
        processo.getProcessosRelacionados().remove(this); // Manter a relação bidirecional
    }

    public void ativar(){
        this.setStatus(StatusProcesso.ATIVO);
    }

    public void arquivar(){
        this.setStatus(StatusProcesso.ARQUIVADO);
    };

    @OneToMany(mappedBy = "processo", cascade = CascadeType.ALL, orphanRemoval = true)
private List<Item> itens = new ArrayList<>();
}

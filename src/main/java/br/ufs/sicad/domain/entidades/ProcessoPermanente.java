package br.ufs.sicad.domain.entidades;

import jakarta.persistence.CascadeType;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.OneToMany;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Entity
@DiscriminatorValue("PERMANENTE")
@Getter
@Setter
@NoArgsConstructor
public class ProcessoPermanente extends Processo {

    @OneToMany(mappedBy = "processo", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<RegistroDistribuicao> registrosDistribuicao = new ArrayList<>();

    public void adicionarRegistroDistribuicao(RegistroDistribuicao registro) {
        this.registrosDistribuicao.add(registro);
        registro.setProcesso(this);
    }
}
package br.ufs.sicad.domain.entidades;

import br.ufs.sicad.config.ValidationException;
import br.ufs.sicad.domain.enums.StatusProcesso;
import br.ufs.sicad.domain.enums.StatusSolicitacaoSpal;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@Entity
@DiscriminatorValue("SPAL")
public class ProcessoSpal extends Processo {
    @OneToMany(mappedBy = "processoSpal", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<SolicitacaoSpal> solicitacoes = new ArrayList<>();

    // Método auxiliar para garantir a consistência da relação bidirecional
    public void adicionarSolicitacao(SolicitacaoSpal solicitacao) {
        solicitacao.setProcessoSpal(this);
        solicitacoes.add(solicitacao);
    }

    public void removerSolicitacao(SolicitacaoSpal solicitacao) {
        solicitacoes.remove(solicitacao);
        solicitacao.setProcessoSpal(null);
    }

    @Override
    public void arquivar() {
        for (SolicitacaoSpal solicitacao: getSolicitacoes()){
            Set<StatusSolicitacaoSpal> statusFinalizados = new HashSet<>();
            statusFinalizados.add(StatusSolicitacaoSpal.REJEITADA);
            statusFinalizados.add(StatusSolicitacaoSpal.APROVADA);
            if (!statusFinalizados.contains(solicitacao.getStatusFluxo())){
                throw new ValidationException("Existem solicitações pendentes, o processo não pode ser arquivado.");
            }
        }
        this.setStatus(StatusProcesso.ARQUIVADO);
    }
}
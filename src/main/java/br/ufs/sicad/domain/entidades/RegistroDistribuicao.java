package br.ufs.sicad.domain.entidades;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDate;
import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "registros_distribuicao")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class RegistroDistribuicao {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    @Column(name = "data_distribuicao", nullable = false)
    private LocalDate dataDistribuicao;

    @NotNull
    @Column(name = "quantidade_distribuida", nullable = false)
    private Integer quantidadeDistribuida;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "unidade_destino_id", nullable = false)
    private UnidadeOrganizacional unidadeDestino;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "item_id", nullable = false)
    private Item item;

   
    @NotNull
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "processo_id", nullable = false)
    private Processo processo;

    @ElementCollection(fetch = FetchType.EAGER)
    @CollectionTable(name = "patrimonios_distribuicao", joinColumns = @JoinColumn(name = "registro_distribuicao_id"))
    @Column(name = "patrimonio", nullable = false)
    private Set<Long> patrimonios = new HashSet<>();

}
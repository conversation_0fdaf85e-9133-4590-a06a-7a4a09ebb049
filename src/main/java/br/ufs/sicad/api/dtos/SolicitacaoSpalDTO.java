package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.SolicitacaoSpal;
import java.time.LocalDate;
import java.util.List;

public record SolicitacaoSpalDTO(
        Long id,
        String numeroSolicitacao,
        LocalDate dataSolicitacao,
        String statusFluxo,
        Integer numeroGlpi,
        String justificativa,
        String analiseTecnica,
        String motivoRejeicao,
        Long processoSpalId,
        // Dados unificados do requerente
        boolean isRequerenteInterno,
        String nomeRequerente,
        String documentoRequerente,
        List<String> patrimonios
) {
    public static SolicitacaoSpalDTO from(SolicitacaoSpal s) {
        return new SolicitacaoSpalDTO(
                s.getId(),
                s.getNumeroSolicitacao(),
                s.getDataSolicitacao(),
                s.getStatusFluxo().name(),
                s.getNumeroGlpi(),
                s.getJustificativa(),
                s.getAnaliseTecnica(),
                s.getMotivoRejeicao(),
                s.getProcessoSpal().getId(),
                s.isRequerenteInterno(),
                s.getNomeRequerente(),
                s.getDocumentoRequerente(),
                s.getPatrimonios()
        );
    }
}
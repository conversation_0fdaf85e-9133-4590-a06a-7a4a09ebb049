package br.ufs.sicad.api.dtos;

import br.ufs.sicad.config.ValidationException;
import br.ufs.sicad.domain.entidades.Processo;
import br.ufs.sicad.domain.entidades.ProcessoSpal;
import br.ufs.sicad.domain.entidades.UnidadeOrganizacional;
import br.ufs.sicad.domain.entidades.Usuario;
import br.ufs.sicad.utils.Utils;

public record AtualizaProcessoForm(
        // Campos Comuns
        String tipo,
        String numeroSei,
        String dataAbertura,
        String justificativa,
        String nomeSolicitanteProcesso,
        Long criadorId,
        Long unidadeRequisitanteId,

        // Campos Específicos de ProcessoConsumo
        String statusEntrega,

        // Campos Específicos de ProcessoServico
        Integer prazoVigenciaContratual,
        String unidadeMedidaPrazo,
        String statusVigencia
) {
    public Processo asProcesso(){
        Processo processo;
        switch (tipo.toUpperCase()){
            case "SPAL" -> {
                ProcessoSpal spal = new ProcessoSpal();
                Usuario criador = new Usuario();
                criador.setId(this.criadorId);
                spal.setCriador(criador);
                UnidadeOrganizacional unidadeRequisitante = new UnidadeOrganizacional();
                unidadeRequisitante.setId(this.unidadeRequisitanteId);
                spal.setUnidadeRequisitante(unidadeRequisitante);
                spal.setNumeroSei(this.numeroSei);
                spal.setDataAbertura(Utils.converter(this.dataAbertura));
                spal.setJustificativa(this.justificativa);
                spal.setNomeSolicitanteProcesso(this.nomeSolicitanteProcesso);
                processo = spal;
            }
            default -> throw new ValidationException("Tipo de processo inválido: " + this.tipo);
        }

        return processo;
    }
}
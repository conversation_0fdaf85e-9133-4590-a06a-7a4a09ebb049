package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.RegistroDistribuicao;
import br.ufs.sicad.domain.entidades.UnidadeOrganizacional;
import br.ufs.sicad.utils.Utils;

import java.util.Set;

public record RegistroDistribuicaoDTO(
        Long id,
        String dataDistribuicao,
        Integer quantidadeDistribuida,
        UnidadeOrganizacional unidadeDestino,
        ItemDTO item,
        ProcessoDTO processo,
        Set<Long> patrimonios) {
    public static RegistroDistribuicaoDTO from(RegistroDistribuicao registroDistribuicao){
        return new RegistroDistribuicaoDTO(
                registroDistribuicao.getId(),
                Utils.converter(registroDistribuicao.getDataDistribuicao()),
                registroDistribuicao.getQuantidadeDistribuida(),
                registroDistribuicao.getUnidadeDestino(),
                ItemDTO.from(registroDistribuicao.getItem()),
                ProcessoDTO.from(registroDistribuicao.getProcesso()),
                registroDistribuicao.getPatrimonios()
        );
    }

}

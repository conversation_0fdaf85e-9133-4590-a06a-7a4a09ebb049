//package br.ufs.sicad.api.dtos;
//
//import br.ufs.sicad.domain.entidades.Documento;
//import br.ufs.sicad.domain.enums.Status;
//import java.time.LocalDateTime;
//
//public record DocumentoDTO(
//        Long id,
//        String nomeArquivoOriginal,
//        String tipoArquivo,
//        LocalDateTime dataUpload,
//        Status status,
//        String urlDownload
//) {
//    public static DocumentoDTO from(Documento documento, String baseUrl) {
//        String downloadUrl = baseUrl + "/documentos/download/" + documento.getNomeArquivoUnico();
//        return new DocumentoDTO(
//                documento.getId(),
//                documento.getNomeArquivoOriginal(),
//                documento.getTipoArquivo(),
//                documento.getDataUpload(),
//                documento.getStatus(),
//                downloadUrl
//        );
//    }
//}
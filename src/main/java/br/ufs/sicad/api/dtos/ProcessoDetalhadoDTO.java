package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.Processo;
import br.ufs.sicad.domain.entidades.ProcessoServico;
import br.ufs.sicad.domain.entidades.ProcessoSpal;

import java.time.LocalDate;
import java.util.List;

public record ProcessoDetalhadoDTO(
        Long id,
        String numeroSei,
        String tipo,
        String modalidade,
        String status,
        LocalDate dataAbertura,
        UnidadeOrganizacionalDTO unidadeRequisitante,
        UsuarioDTO criador,
        // Campos Específicos de ProcessoSPAL
        List<SolicitacaoSpalDTO>solicitacoes,
//        Campos Especificos para processo servico
        List<RegistroDistribuicaoDTO> registroDistribuicaoDTO
//        Campos Especificos para outros tipos de processo
//        List<ItemDto> itens
//        List<ContratoDTO> contratos
) {
    public static ProcessoDetalhadoDTO from(Processo processo) {
        if (processo instanceof ProcessoSpal processoSpal){
            return new ProcessoDetalhadoDTO(
                processoSpal.getId(),
                processoSpal.getNumeroSei(),
                processoSpal.getClass().getSimpleName().replace("Processo", "").toUpperCase(),
                processoSpal.getModalidade().name(),
                processoSpal.getStatus().name(),
                processoSpal.getDataAbertura(),
                UnidadeOrganizacionalDTO.from(processoSpal.getUnidadeRequisitante()),
                UsuarioDTO.from(processoSpal.getCriador()),
                processoSpal.getSolicitacoes().stream().map(SolicitacaoSpalDTO::from).toList(),
                null
            );
        }
        if (processo instanceof ProcessoServico processoServico){
            return new ProcessoDetalhadoDTO(
                    processoServico.getId(),
                    processoServico.getNumeroSei(),
                    processoServico.getClass().getSimpleName().replace("Processo", "").toUpperCase(),
                    processoServico.getModalidade().name(),
                    processoServico.getStatus().name(),
                    processoServico.getDataAbertura(),
                    UnidadeOrganizacionalDTO.from(processoServico.getUnidadeRequisitante()),
                    UsuarioDTO.from(processoServico.getCriador()),
                    null,
                    processoServico.getRegistrosDistribuicao().stream().map(RegistroDistribuicaoDTO::from).toList()
            );
        }
        return null;
    }
}
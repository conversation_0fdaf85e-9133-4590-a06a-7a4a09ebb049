package br.ufs.sicad.api.dtos;

import br.ufs.sicad.domain.entidades.Processo;


import java.time.LocalDate;

public record ProcessoDTO(
        Long id,
        String numeroSei,
        String tipo,
        String modalidade,
        String status,
        LocalDate dataAbertura,
        UnidadeOrganizacionalDTO unidadeRequisitante,
        UsuarioDTO criador 
) {
    public static ProcessoDTO from(Processo processo) {
        return new ProcessoDTO(
                processo.getId(),
                processo.getNumeroSei(),
                processo.getClass().getSimpleName().replace("Processo", "").toUpperCase(),
                processo.getModalidade().name(),
                processo.getStatus().name(),
                processo.getDataAbertura(),
                UnidadeOrganizacionalDTO.from(processo.getUnidadeRequisitante()),
                UsuarioDTO.from(processo.getCriador()) 
        );
    }
}
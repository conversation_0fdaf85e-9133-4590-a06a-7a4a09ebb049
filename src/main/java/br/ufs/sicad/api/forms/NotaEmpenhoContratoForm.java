package br.ufs.sicad.api.forms;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

import java.time.LocalDate;
import java.util.List;

/**
 * Form para criação de Nota de Empenho baseada em um Contrato existente.
 * Este form permite selecionar itens de um contrato específico e suas quantidades.
 */
public record NotaEmpenhoContratoForm(
        @NotNull(message = "O número da nota de empenho é obrigatório")
        String numero,

        @NotNull(message = "A data de emissão é obrigatória")
        @JsonFormat(pattern = "dd/MM/yyyy")
        LocalDate dataEmissao,

        @NotNull(message = "O ID do contrato é obrigatório")
        Integer contratoId,

        @NotNull(message = "O ID do fornecedor é obrigatório")
        Long fornecedorId,

        @NotNull(message = "O prazo máximo de entrega é obrigatório")
        @JsonFormat(pattern = "dd/MM/yyyy")
        LocalDate prazoMaximoEntrega,

        @NotNull(message = "Os dias de notificação prévia são obrigatórios")
        @Positive(message = "Os dias de notificação prévia devem ser positivos")
        Integer diasNotificacaoPrevia,

        @NotEmpty(message = "A nota deve ter pelo menos um item do contrato")
        @Valid
        List<NotaEmpenhoContratoItemForm> itens
) {
}

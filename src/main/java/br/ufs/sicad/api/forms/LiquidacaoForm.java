package br.ufs.sicad.api.forms;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class LiquidacaoForm {

    @NotNull(message = "A quantidade a liquidar é obrigatória")
    @Positive(message = "A quantidade deve ser maior que zero")
    private Integer quantidade;

    @NotNull(message = "O valor a liquidar é obrigatório")
    @Positive(message = "O valor deve ser maior que zero")
    private BigDecimal valor;
}
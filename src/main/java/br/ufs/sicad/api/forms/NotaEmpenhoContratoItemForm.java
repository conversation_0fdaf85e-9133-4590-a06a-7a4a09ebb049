package br.ufs.sicad.api.forms;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

import java.math.BigDecimal;

/**
 * Form para representar um item do contrato selecionado para a nota de empenho.
 * Contém o ID do item e a quantidade desejada.
 */
public record NotaEmpenhoContratoItemForm(
        @NotNull(message = "O ID do item é obrigatório")
        Long itemId,

        @NotNull(message = "A quantidade é obrigatória")
        @Positive(message = "A quantidade deve ser positiva")
        Integer quantidade,

        @NotNull(message = "O valor unitário é obrigatório")
        @Positive(message = "O valor unitário deve ser positivo")
        BigDecimal valorUnitario,

        String observacoes
) {
}

package br.ufs.sicad.api.forms;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.time.LocalDate;
import java.util.List;

// NOVO: Este form é para a atualização e INCLUI a lista de itens.
public record NotaEmpenhoUpdateComItensForm(
        @NotBlank String numero,
        @NotNull @JsonFormat(pattern = "dd/MM/yyyy") LocalDate dataEmissao,
        @NotNull @JsonFormat(pattern = "dd/MM/yyyy") LocalDate prazoMaximoEntrega,
        @NotNull @Positive Integer diasNotificacaoPrevia,
        @NotEmpty @Valid List<NotaEmpenhoItemForm> itens
) {}
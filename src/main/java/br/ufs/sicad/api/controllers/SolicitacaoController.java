package br.ufs.sicad.api.controllers;

import br.ufs.sicad.api.dtos.*;
import br.ufs.sicad.config.ValidationException;
import br.ufs.sicad.domain.entidades.Processo;
import br.ufs.sicad.domain.entidades.ProcessoSpal;
import br.ufs.sicad.domain.entidades.SolicitacaoSpal;
import br.ufs.sicad.services.ProcessoService;
import br.ufs.sicad.services.SolicitacaoSpalService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("processos/{id}/solicitacoes")
@RequiredArgsConstructor
public class SolicitacaoController {

    private final ProcessoService processoService;
    private final SolicitacaoSpalService solicitacaoSpalService;

    @PostMapping
    public ResponseEntity<ProcessoDetalhadoDTO> adicionarSolicitacaoSpal(@PathVariable Long id, @RequestBody SolicitacaoSpalForm solicitacao){
        Processo processoSpal = processoService.buscarProcessoPorId(id);
        if (!(processoSpal instanceof ProcessoSpal)){
            throw new ValidationException("Esse processo não é um proceso Spal.");
        }
        Processo processoAtualizado = solicitacaoSpalService.adicionarSolicitacaoSpal((ProcessoSpal) processoSpal , solicitacao.requerenteInternoId(), solicitacao.asSolicitacao());
        return ResponseEntity.status(HttpStatus.CREATED).body(ProcessoDetalhadoDTO.from(processoAtualizado));
    }

    @PutMapping("{solicitacaoId}/parecer")
    public ResponseEntity<SolicitacaoSpalDTO> adicionarParecerTecnico(@PathVariable Long id, @PathVariable Long solicitacaoId, @RequestBody ParecerForm parecerForm){
        Processo processoSpal = processoService.buscarProcessoPorId(id);
        if (!(processoSpal instanceof ProcessoSpal)){
            throw new ValidationException("Esse processo não é um proceso Spal.");
        }
        SolicitacaoSpal solicitacaoSpal = solicitacaoSpalService.registrarParecerTecnico(solicitacaoId, parecerForm.parecerTecnico());
        return ResponseEntity.status(HttpStatus.CREATED).body(SolicitacaoSpalDTO.from(solicitacaoSpal));
    }

    @PutMapping("{solicitacaoId}/aprovar")
    public ResponseEntity<SolicitacaoSpalDTO> aprovarSolicitacao(@PathVariable Long id, @PathVariable Long solicitacaoId){
        Processo processoSpal = processoService.buscarProcessoPorId(id);
        if (!(processoSpal instanceof ProcessoSpal)){
            throw new ValidationException("Esse processo não é um proceso Spal.");
        }
        SolicitacaoSpal solicitacaoSpal = solicitacaoSpalService.aprovar(solicitacaoId);
        return ResponseEntity.status(HttpStatus.OK).body(SolicitacaoSpalDTO.from(solicitacaoSpal));
    }

    @PutMapping("{solicitacaoId}/recusar")
    public ResponseEntity<SolicitacaoSpalDTO> recusarSolicitacao(@PathVariable Long id, @PathVariable Long solicitacaoId, @RequestBody RecusarSolicitacaoForm recusarForm){
        Processo processoSpal = processoService.buscarProcessoPorId(id);
        if (!(processoSpal instanceof ProcessoSpal)){
            throw new ValidationException("Esse processo não é um proceso Spal.");
        }
        SolicitacaoSpal solicitacaoSpal = solicitacaoSpalService.recusar(solicitacaoId, recusarForm.justificativa());
        return ResponseEntity.status(HttpStatus.OK).body(SolicitacaoSpalDTO.from(solicitacaoSpal));
    }

    @DeleteMapping("{solicitacaoId}")
    public ResponseEntity<Void> arquivar(@PathVariable Long id, @PathVariable Long solicitacaoId){
        Processo processoSpal = processoService.buscarProcessoPorId(id);
        if (!(processoSpal instanceof ProcessoSpal)){
            throw new ValidationException("Esse processo não é um proceso Spal.");
        }
        solicitacaoSpalService.arquivarSolicitacao(solicitacaoId);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

}

package br.ufs.sicad.api.controllers;

import br.ufs.sicad.api.dtos.UnidadeOrganizacionalDTO;
import br.ufs.sicad.domain.entidades.UnidadeOrganizacional;
import br.ufs.sicad.domain.enums.Status;
import br.ufs.sicad.services.UnidadeOrganizacionalService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/unidades-organizacionais")
@RequiredArgsConstructor
public class UnidadeOrganizacionalController {

    private final UnidadeOrganizacionalService service;

    @GetMapping
    public ResponseEntity<Page<UnidadeOrganizacionalDTO>> listarTodos(
            @RequestParam(required = false) String nome,
            @RequestParam(required = false) String sigla,
            @RequestParam(required = false) String status,
            Pageable pageable) {
        
        Status statusEnum = null;
        if (status != null && !status.trim().isEmpty()) {
            statusEnum = Status.valueOf(status.toUpperCase());
        }
        
        Page<UnidadeOrganizacional> unidades = service.listarComFiltros(nome, sigla, statusEnum, pageable);
        Page<UnidadeOrganizacionalDTO> dtos = unidades.map(UnidadeOrganizacionalDTO::from);
        return ResponseEntity.ok(dtos);
    }

    @GetMapping("/{id}")
    public ResponseEntity<UnidadeOrganizacionalDTO> buscarPorId(@PathVariable Long id) {
        UnidadeOrganizacional unidade = service.buscarPorId(id);
        return ResponseEntity.ok(UnidadeOrganizacionalDTO.from(unidade));
    }

    @PostMapping
    public ResponseEntity<UnidadeOrganizacionalDTO> criar(@RequestBody @Valid UnidadeOrganizacionalDTO dto) {
        UnidadeOrganizacional unidade = service.criar(dto.nome(), dto.sigla(), dto.unidadePaiId());
        return ResponseEntity.status(HttpStatus.CREATED).body(UnidadeOrganizacionalDTO.from(unidade));
    }

    @PutMapping("/{id}")
    public ResponseEntity<UnidadeOrganizacionalDTO> atualizar(@PathVariable Long id, @RequestBody @Valid UnidadeOrganizacionalDTO dto) {
        UnidadeOrganizacional unidade = service.atualizar(id, dto.nome(), dto.sigla(), dto.unidadePaiId());
        return ResponseEntity.ok(UnidadeOrganizacionalDTO.from(unidade));
    }


    @PutMapping("/reativar/{id}")
    public ResponseEntity<Void> ativar(@PathVariable Long id) {
        service.ativar(id);
        return ResponseEntity.noContent().build();
    }

    @DeleteMapping("{id}")
    public ResponseEntity<Void> inativar(@PathVariable Long id) {
        service.inativar(id);
        return ResponseEntity.noContent().build();
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Map<String, String> handleValidationExceptions(MethodArgumentNotValidException ex) {
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getFieldErrors().forEach(error -> {
            String fieldName = error.getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });
        return errors;
    }
}
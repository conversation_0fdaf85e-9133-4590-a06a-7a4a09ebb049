//package br.ufs.sicad.api.controllers;
//
//import br.ufs.sicad.api.dtos.DocumentoDTO;
//import br.ufs.sicad.domain.entidades.Documento;
//import br.ufs.sicad.domain.entidades.UnidadeOrganizacional;
//import br.ufs.sicad.domain.entidades.Usuario;
//import br.ufs.sicad.services.DocumentoService;
//import br.ufs.sicad.services.UsuarioService;
//import lombok.RequiredArgsConstructor;
//import org.springframework.core.io.Resource;
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.MediaType;
//import org.springframework.http.ResponseEntity;
//import org.springframework.security.access.AccessDeniedException;
//import org.springframework.web.bind.annotation.*;
//import org.springframework.web.multipart.MultipartFile;
//import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
//
//import java.io.IOException;
//import java.nio.file.Files;
//import java.security.Principal;
//
//@RestController
//@RequestMapping("/documentos")
//@RequiredArgsConstructor
//public class DocumentoController {
//
//    private final DocumentoService documentoService;
//    private final UsuarioService usuarioService;
//
//    @PostMapping("/upload")
//    public ResponseEntity<DocumentoDTO> upload(
//            @RequestParam("file") MultipartFile file,
//            @RequestParam("unidadeId") Long unidadeId,
//            Principal principal) {
////
////        String emailUsuarioLogado = principal.getName();
////        Usuario usuarioLogado = usuarioService.buscarPorEmail(emailUsuarioLogado);
////
////        // Verificação de segurança
////        UnidadeOrganizacional unidadeDoDocumento = usuarioLogado.getUnidadesOrganizacionais().stream()
////                .filter(unidade -> unidade.getId().equals(unidadeId))
////                .findFirst()
////                .orElseThrow(() -> new AccessDeniedException("O usuário não tem permissão para usar esta Unidade Organizacional."));
////
////        Documento doc = documentoService.salvarArquivo(file, unidadeDoDocumento, usuarioLogado);
////
////        String baseUrl = ServletUriComponentsBuilder.fromCurrentContextPath().build().toUriString();
////        DocumentoDTO dto = DocumentoDTO.from(doc, baseUrl);
////
////        return ResponseEntity.status(HttpStatus.CREATED).body(dto);
//        return null;
//    }
//
//    @GetMapping("/{id}")
//    public ResponseEntity<DocumentoDTO> buscarInfoPorId(@PathVariable Long id) {
//        Documento doc = documentoService.buscarPorId(id);
//        String baseUrl = ServletUriComponentsBuilder.fromCurrentContextPath().build().toUriString();
//        return ResponseEntity.ok(DocumentoDTO.from(doc, baseUrl));
//    }
//
//    @GetMapping("/download/{nomeArquivoUnico:.+}")
//    public ResponseEntity<Resource> download(@PathVariable String nomeArquivoUnico) {
//        Resource resource = documentoService.carregarComoRecurso(nomeArquivoUnico);
//        String contentType = "application/octet-stream";
//        try {
//            contentType = Files.probeContentType(resource.getFile().toPath());
//        } catch (IOException e) {
//            // Log do erro se necessário
//        }
//
//        return ResponseEntity.ok()
//                .contentType(MediaType.parseMediaType(contentType))
//                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + resource.getFilename() + "\"")
//                .body(resource);
//    }
//
//    @PatchMapping("/{id}/inativar")
//    @ResponseStatus(HttpStatus.NO_CONTENT)
//    public void inativar(@PathVariable Long id) {
//        documentoService.inativar(id);
//    }
//}
package br.ufs.sicad.api.controllers;

import br.ufs.sicad.api.dtos.NotaEmpenhoDTO;
import br.ufs.sicad.api.dtos.NotaEmpenhoDetalhadaDTO;
import br.ufs.sicad.api.forms.LiquidacaoForm;
import br.ufs.sicad.api.forms.NotaEmpenhoComItensForm;
import br.ufs.sicad.api.forms.NotaEmpenhoContratoForm;
import br.ufs.sicad.api.forms.NotaEmpenhoUpdateComItensForm;
import br.ufs.sicad.domain.entidades.NotaEmpenho;
import br.ufs.sicad.services.NotaEmpenhoService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;

@RestController
@RequestMapping("/notas-empenho")
@RequiredArgsConstructor
public class NotaEmpenhoController {

    private final NotaEmpenhoService notaEmpenhoService;

    @GetMapping
    public ResponseEntity<Page<NotaEmpenhoDTO>> listarTodos(
            @RequestParam(required = false) String numero,
            @RequestParam(required = false, defaultValue = "TODAS") String status,
            Pageable pageable) {
        Page<NotaEmpenho> notasEmpenho = notaEmpenhoService.listarComFiltros(numero, status, pageable);
        return ResponseEntity.ok(notasEmpenho.map(NotaEmpenhoDTO::from));
    }

    @GetMapping("/{id}")
    public ResponseEntity<NotaEmpenhoDetalhadaDTO> buscarPorId(@PathVariable Long id) {
        NotaEmpenho notaEmpenho = notaEmpenhoService.buscarPorId(id);
        return ResponseEntity.ok(NotaEmpenhoDetalhadaDTO.from(notaEmpenho));
    }

    @PostMapping
    public ResponseEntity<NotaEmpenhoDetalhadaDTO> criar(@RequestBody @Valid NotaEmpenhoComItensForm form) {
        NotaEmpenho notaEmpenho = notaEmpenhoService.criarComItens(form);
        return ResponseEntity.status(HttpStatus.CREATED).body(NotaEmpenhoDetalhadaDTO.from(notaEmpenho));
    }

    @PutMapping("/{id}")
    public ResponseEntity<NotaEmpenhoDetalhadaDTO> atualizar(@PathVariable Long id,
                                                             @RequestBody @Valid NotaEmpenhoUpdateComItensForm form) {
        NotaEmpenho notaEmpenho = notaEmpenhoService.atualizar(id, form);
        return ResponseEntity.ok(NotaEmpenhoDetalhadaDTO.from(notaEmpenho));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deletar(@PathVariable Long id) {
        notaEmpenhoService.deletar(id);
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/{id}/reativa")
    public ResponseEntity<Void> reativar(@PathVariable Long id) {
        notaEmpenhoService.reativar(id);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/{id}/anexo")
    public ResponseEntity<Void> adicionarAnexo(@PathVariable Long id, @RequestParam("arquivo") MultipartFile arquivo) throws IOException {
        notaEmpenhoService.adicionarAnexo(id, arquivo);
        return ResponseEntity.ok().build();
    }

    @DeleteMapping("/{id}/anexo")
    public ResponseEntity<Void> removerAnexo(@PathVariable Long id) throws IOException {
        notaEmpenhoService.removerAnexo(id);
        return ResponseEntity.noContent().build();
    }

    // ***** NOVO ENDPOINT PARA LIQUIDAÇÃO *****
    /**
     * Liquida (parcial ou totalmente) um item específico de uma nota de empenho.
     * @param itemEmpenhoId O ID da linha na tabela `nota_empenho_item`.
     * @param form O corpo da requisição com a quantidade e valor a liquidar.
     * @return Resposta vazia com status 200 OK.
     */
    @PostMapping("/itens/{itemEmpenhoId}/liquidar")
    public ResponseEntity<Void> liquidarItem(@PathVariable Long itemEmpenhoId, @RequestBody @Valid LiquidacaoForm form) {
        notaEmpenhoService.liquidarItem(itemEmpenhoId, form.getQuantidade(), form.getValor());
        return ResponseEntity.ok().build();
    }

    /**
     * Cria uma nova nota de empenho baseada em um contrato existente.
     * Os itens são selecionados do contrato e o valor total é calculado automaticamente.
     * @param form Dados da nota de empenho baseada em contrato
     * @return A nota de empenho criada com todos os detalhes
     */
    @PostMapping("/contrato")
    public ResponseEntity<NotaEmpenhoDetalhadaDTO> criarNotaEmpenhoDeContrato(@RequestBody @Valid NotaEmpenhoContratoForm form) {
        NotaEmpenho notaEmpenho = notaEmpenhoService.criarNotaEmpenhoDeContrato(form);
        return ResponseEntity.status(HttpStatus.CREATED).body(NotaEmpenhoDetalhadaDTO.from(notaEmpenho));
    }
}
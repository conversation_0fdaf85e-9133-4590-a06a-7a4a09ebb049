package br.ufs.sicad.api.controllers;

import br.ufs.sicad.api.dtos.*;
import br.ufs.sicad.config.ValidationException;
import br.ufs.sicad.domain.entidades.Processo;
import br.ufs.sicad.domain.enums.Modalidade;
import br.ufs.sicad.domain.enums.StatusProcesso;
import br.ufs.sicad.services.ProcessoService;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;


@RestController
@RequestMapping("/processos") // Caminho base da API
public class ProcessoController {

    private final ProcessoService processoService;

    public ProcessoController(ProcessoService processoService) {
        this.processoService = processoService;
    }

    @GetMapping
    public ResponseEntity<Page<ProcessoDTO>> listarProcessos(@RequestParam(required = false) String numeroSEI,
                                                             @RequestParam(required = false) String tipo,
                                                             @RequestParam(required = false) String modalidade,
                                                             @RequestParam(required = false) String status,
                                                             @RequestParam(defaultValue = "0") int page,
                                                             @RequestParam(defaultValue = "10") int size) {

        StatusProcesso statusProcesso = null;
        Modalidade modalidadeProcesso = null;
        try {
            if (status != null && !status.isBlank()) {
                statusProcesso = StatusProcesso.valueOf(status.toUpperCase());
            }
            if (modalidade != null && !modalidade.isBlank()) {
                modalidadeProcesso = Modalidade.valueOf(modalidade.toUpperCase());
            }
        } catch (IllegalArgumentException ex){
            throw new ValidationException("Filtro utilizado não encontrado");
        }

        Pageable pageable = PageRequest.of(page, size);
        Page<Processo> processos = processoService.listarProcessos(
                numeroSEI,
                tipo,
                modalidadeProcesso,
                statusProcesso,
                pageable);
        List<ProcessoDTO> processosDTO = processos.getContent().stream()
                .map(ProcessoDTO::from).toList();

        Page<ProcessoDTO> processosPage = new PageImpl<>(processosDTO, pageable, processos.getTotalElements());

        return ResponseEntity.ok(processosPage);
    }

    @GetMapping("/{id}")
    public ResponseEntity<ProcessoDetalhadoDTO> buscarProcesso(@PathVariable Long id){
        Processo processo = processoService.buscarProcessoPorId(id);
        return ResponseEntity.ok(ProcessoDetalhadoDTO.from(processo));
    }

    @PostMapping
    public ResponseEntity<ProcessoDTO> criarProcesso(@RequestBody @Valid ProcessoForm processoForm) {
        Processo processoSalvo = processoService.criarProcesso(processoForm.asProcesso(), processoForm.processosRelacionados());
        return ResponseEntity.status(HttpStatus.CREATED).body(ProcessoDTO.from(processoSalvo));
    }

    @PutMapping("/{id}")
    public ResponseEntity<ProcessoDTO> atualizarProcesso(@PathVariable Long id, @RequestBody @Valid AtualizaProcessoForm processoForm){
        Processo processoAtualizado = processoService.atualizarProcesso(id, processoForm.asProcesso());
        return ResponseEntity.ok(ProcessoDTO.from(processoAtualizado));
    }

    @PostMapping("/{id}/distribuicoes")
    public ResponseEntity<ProcessoDTO> adicionarRegistroDistribuicao(
            @PathVariable Long id,
            @RequestBody @Valid RegistroDistribuicaoForm form) {
        Processo processoAtualizado = processoService.adicionarRegistroDistribuicao(id, form);
        return ResponseEntity.status(HttpStatus.CREATED).body(ProcessoDTO.from(processoAtualizado));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> arquivarProcesso(@PathVariable Long id){
        processoService.arquivarProcesso(id);
        return ResponseEntity.noContent().build();
    }

    @PutMapping("/{id}/desarquivar")
    public ResponseEntity<ProcessoDTO> desarquivarProcesso(@PathVariable Long id) {
        Processo processoDesarquivado = processoService.ativarProcesso(id);
        return ResponseEntity.ok(ProcessoDTO.from(processoDesarquivado));
    }
}